# Krok 4: Service vrstva a business logika - DOKONČENO

## P<PERSON>ehled implementace

Krok 4 byl ú<PERSON><PERSON><PERSON><PERSON><PERSON> do<PERSON>. Implementována byla kompletní service vrstva s business logikou pro správu katalogů, souborů, emailů a cache.

## Implementované komponenty

### 1. CatalogManager.php
**Umístění:** `src/Service/CatalogManager.php`

**Hlavní funkcionality:**
- ✅ `createCatalog()` - vytvoření nového katalogu s validací a file upload
- ✅ `updateCatalog()` - aktualizace katalogu včetně správy souborů
- ✅ `deleteCatalog()` - smaz<PERSON><PERSON> katalogu včetně souvisejících souborů
- ✅ `toggleActive()` - přepnutí aktivního stavu
- ✅ `reorderCatalogs()` - přeuspořádán<PERSON> pozic katalogů
- ✅ `duplicateCatalog()` - duplikace katalogu včetně souborů
- ✅ `bulkDelete()` - hromadné smazání
- ✅ `bulkToggleActive()` - hromadné přepnutí stavu
- ✅ `getStatistics()` - statistiky katalogů s cache

**Business pravidla:**
- Kontrola unikátnosti názvů
- Automatická správa pozic
- Validace dat před uložením
- Cache invalidation při změnách

### 2. FileUploadService.php
**Umístění:** `src/Service/FileUploadService.php`

**Hlavní funkcionality:**
- ✅ `uploadImage()` - upload obrázků s optimalizací
- ✅ `uploadCatalogFile()` - upload souborů katalogů (PDF, ZIP)
- ✅ `deleteFile()` - bezpečné smazání souborů
- ✅ `duplicateFile()` - duplikace souborů
- ✅ `validateImageFile()` - validace obrázků
- ✅ `validateCatalogFile()` - validace souborů

**Bezpečnostní funkce:**
- MIME type validace
- Kontrola velikosti souborů
- Bezpečné názvy souborů
- .htaccess ochrana adresářů
- Automatické vytvoření adresářové struktury

### 3. ImageOptimizer.php
**Umístění:** `src/Service/ImageOptimizer.php`

**Hlavní funkcionality:**
- ✅ `optimizeAndResize()` - vytvoření více velikostí obrázků
- ✅ `createResizedImage()` - změna velikosti se zachováním poměru
- ✅ `optimizeExisting()` - optimalizace existujících obrázků
- ✅ `createThumbnailCrop()` - thumbnail s přesným cropem
- ✅ `addWatermark()` - přidání watermarku
- ✅ `getImageInfo()` - informace o obrázku
- ✅ `checkGdSupport()` - kontrola GD podpory

**Podporované formáty:**
- JPEG, PNG, WebP, GIF
- Automatická konverze do WebP pro lepší kompresi
- Zachování průhlednosti u PNG

### 4. EmailService.php
**Umístění:** `src/Service/EmailService.php`

**Hlavní funkcionality:**
- ✅ `sendOrderNotification()` - notifikace administrátorovi
- ✅ `sendOrderConfirmation()` - potvrzení zákazníkovi
- ✅ `sendAdminNotification()` - admin upozornění
- ✅ `sendBulkEmail()` - hromadné emaily
- ✅ `testEmailConfiguration()` - test email konfigurace
- ✅ `queueBulkEmails()` - email queue systém

**Integrace s PrestaShop:**
- Použití PrestaShop Mail třídy
- Respektování shop konfigurace
- Multijazyčná podpora

### 5. EmailTemplateManager.php
**Umístění:** `src/Service/EmailTemplateManager.php`

**Hlavní funkcionality:**
- ✅ `renderTemplate()` - renderování templates s cache
- ✅ `createTemplate()` - vytvoření nového template
- ✅ `updateTemplate()` - aktualizace template s backup
- ✅ `validateTemplate()` - validace PHP syntaxe
- ✅ `exportTemplates()` - export všech templates
- ✅ `importTemplates()` - import ze zálohy

**Výchozí templates:**
- order_notification - notifikace o objednávce (HTML + text)
- order_confirmation - potvrzení objednávky (HTML + text)
- admin_alert - admin upozornění (HTML + text)

### 6. CatalogOrderService.php
**Umístění:** `src/Service/CatalogOrderService.php`

**Hlavní funkcionality:**
- ✅ `createOrder()` - vytvoření objednávky s validací
- ✅ `validateOrderData()` - komplexní validace dat
- ✅ `processOrder()` - zpracování objednávky (emaily)
- ✅ `getOrderStatistics()` - statistiky objednávek
- ✅ `exportOrdersToCsv()` - export do CSV
- ✅ `cleanupOldOrders()` - čištění starých objednávek
- ✅ `findDuplicateOrders()` - detekce duplikátů

**Ochranné mechanismy:**
- Rate limiting (max 5 objednávek/hodinu z IP)
- Spam protection (min 10 minut mezi objednávkami ze stejného emailu)
- Validace českého IČO
- Email duplicity kontrola

### 7. CacheService.php
**Umístění:** `src/Service/CacheService.php`

**Hlavní funkcionality:**
- ✅ `get()`, `set()`, `delete()` - základní cache operace
- ✅ `clear()` - vyčištění podle patternu
- ✅ `remember()` - cache-aside pattern
- ✅ `increment()`, `decrement()` - atomické operace
- ✅ `cleanupExpired()` - čištění expirovaných záznamů
- ✅ `backup()`, `restore()` - záloha a obnova cache

**Cache strategie:**
- catalog_list: 1 hodina
- catalog_detail: 2 hodiny
- configuration: 24 hodin
- image_metadata: 1 týden
- statistics: 30 minut

## Aktualizované konfigurace

### services.yml
Aktualizována konfigurace služeb pro registraci všech nových service tříd:
- Správné dependency injection
- Předání module directory path
- Veřejné služby pro admin controllery

## Adresářová struktura

```
src/Service/
├── CacheService.php              # Cache management
├── CatalogManager.php            # Hlavní business logika
├── CatalogOrderService.php       # Správa objednávek
├── EmailService.php              # Email komunikace
├── EmailTemplateManager.php      # Email templates
├── FileUploadService.php         # Upload souborů
└── ImageOptimizer.php            # Optimalizace obrázků
```

## Bezpečnostní opatření

### File Upload Security
- MIME type validace
- Kontrola velikosti souborů
- Bezpečné názvy souborů
- .htaccess ochrana
- Malware scanning připravenost

### Rate Limiting & Spam Protection
- IP-based rate limiting
- Email-based spam protection
- Configurable limity
- Cache-based tracking

### Data Validation
- Comprehensive input validation
- Czech IČO validation
- Email format validation
- Business rules enforcement

## Performance optimalizace

### Cache Strategie
- Inteligentní cache TTL podle typu dat
- Pattern-based cache invalidation
- Atomic cache operations
- Backup/restore mechanismus

### Image Optimization
- Multiple size generation
- WebP conversion for better compression
- Lazy loading připravenost
- Memory efficient processing

### Database Optimization
- Batch operations support
- Connection pooling připravenost
- Query optimization

## Error Handling

### Structured Exceptions
- Domain-specific exceptions
- User-friendly error messages
- Detailed logging
- Graceful degradation

### Logging Integration
- Comprehensive error logging
- Performance monitoring
- Security event logging
- Debug information

## Testování

### Unit Tests Připravenost
- Mockable dependencies
- Interface-based design
- Testable business logic
- Isolated components

### Integration Tests
- Email sending tests
- File upload tests
- Cache functionality tests
- Database operations tests

## Následující kroky

Krok 4 je kompletně dokončen. Připraveno pro:

1. **Krok 5: Administrační rozhraní - základní CRUD**
   - Admin controllery budou využívat implementované služby
   - Formuláře pro správu katalogů
   - Bulk operace v admin rozhraní

2. **Testování service vrstvy**
   - Unit testy pro všechny služby
   - Integration testy pro email a file upload
   - Performance testy pro cache

## Poznámky k implementaci

### Dependency Injection
- Všechny služby jsou správně registrované v services.yml
- Circular dependencies jsou předcházeny
- Lazy loading připravenost

### PrestaShop Kompatibilita
- Respektování PS konvencí
- Integrace s PS Mail systémem
- Využití PS konfigurace

### Rozšiřitelnost
- Interface-based design
- Plugin architecture připravenost
- Event system integrace
- API-ready struktura

Krok 4 poskytuje solidní základ pro business logiku modulu s důrazem na bezpečnost, výkon a udržitelnost kódu.
