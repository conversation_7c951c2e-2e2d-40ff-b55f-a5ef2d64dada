<?php
/**
 * Catalog Order Entity Class
 * 
 * <AUTHOR>
 * @copyright 2024 Cigogne
 * @license   Commercial
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class CatalogOrder extends ObjectModel
{
    /** @var int ID of the catalog */
    public $id_catalog;
    
    /** @var string Company name */
    public $company_name;
    
    /** @var string Company ID (IČO) */
    public $company_id;
    
    /** @var string First name */
    public $first_name;
    
    /** @var string Last name */
    public $last_name;
    
    /** @var string Email address */
    public $email;
    
    /** @var string Phone number */
    public $phone;
    
    /** @var string Address */
    public $address;
    
    /** @var string Note */
    public $note;
    
    /** @var string Date of creation */
    public $date_add;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'cig_catalog_order',
        'primary' => 'id_order',
        'fields' => [
            'id_catalog' => [
                'type' => self::TYPE_INT,
                'validate' => 'isUnsignedInt',
                'required' => true
            ],
            'company_name' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isGenericName',
                'size' => 255
            ],
            'company_id' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isGenericName',
                'size' => 50
            ],
            'first_name' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isName',
                'required' => true,
                'size' => 100
            ],
            'last_name' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isName',
                'required' => true,
                'size' => 100
            ],
            'email' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isEmail',
                'required' => true,
                'size' => 255
            ],
            'phone' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isPhoneNumber',
                'size' => 50
            ],
            'address' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isAddress',
                'required' => true
            ],
            'note' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isCleanHtml'
            ],
            'date_add' => [
                'type' => self::TYPE_DATE,
                'validate' => 'isDate'
            ]
        ]
    ];

    /**
     * Add order to database
     */
    public function add($auto_date = true, $null_values = false)
    {
        if ($auto_date) {
            $this->date_add = date('Y-m-d H:i:s');
        }
        
        return parent::add($auto_date, $null_values);
    }

    /**
     * Get all orders
     */
    public static function getAllOrders($start = 0, $limit = 50, $order_by = 'date_add', $order_way = 'DESC')
    {
        $sql = new DbQuery();
        $sql->select('co.*, c.title as catalog_title');
        $sql->from('cig_catalog_order', 'co');
        $sql->leftJoin('cig_catalog', 'c', 'co.id_catalog = c.id_catalog');
        $sql->orderBy($order_by . ' ' . $order_way);
        $sql->limit($limit, $start);

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Get orders by catalog
     */
    public static function getOrdersByCatalog($id_catalog, $start = 0, $limit = 50)
    {
        $sql = new DbQuery();
        $sql->select('co.*, c.title as catalog_title');
        $sql->from('cig_catalog_order', 'co');
        $sql->leftJoin('cig_catalog', 'c', 'co.id_catalog = c.id_catalog');
        $sql->where('co.id_catalog = ' . (int)$id_catalog);
        $sql->orderBy('co.date_add DESC');
        $sql->limit($limit, $start);

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Get orders by date range
     */
    public static function getOrdersByDateRange($date_from, $date_to, $start = 0, $limit = 50)
    {
        $sql = new DbQuery();
        $sql->select('co.*, c.title as catalog_title');
        $sql->from('cig_catalog_order', 'co');
        $sql->leftJoin('cig_catalog', 'c', 'co.id_catalog = c.id_catalog');
        $sql->where('co.date_add >= "' . pSQL($date_from) . '" AND co.date_add <= "' . pSQL($date_to) . '"');
        $sql->orderBy('co.date_add DESC');
        $sql->limit($limit, $start);

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Get orders count
     */
    public static function getOrdersCount($id_catalog = null)
    {
        $sql = new DbQuery();
        $sql->select('COUNT(*)');
        $sql->from('cig_catalog_order');
        
        if ($id_catalog) {
            $sql->where('id_catalog = ' . (int)$id_catalog);
        }

        return (int)Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);
    }

    /**
     * Get orders statistics
     */
    public static function getOrdersStatistics()
    {
        $sql = new DbQuery();
        $sql->select('
            COUNT(*) as total_orders,
            COUNT(DISTINCT id_catalog) as catalogs_ordered,
            COUNT(DISTINCT email) as unique_customers,
            DATE(date_add) as order_date,
            COUNT(*) as daily_orders
        ');
        $sql->from('cig_catalog_order');
        $sql->where('date_add >= DATE_SUB(NOW(), INTERVAL 30 DAY)');
        $sql->groupBy('DATE(date_add)');
        $sql->orderBy('order_date DESC');

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Get popular catalogs
     */
    public static function getPopularCatalogs($limit = 10)
    {
        $sql = new DbQuery();
        $sql->select('
            co.id_catalog,
            c.title,
            COUNT(*) as order_count
        ');
        $sql->from('cig_catalog_order', 'co');
        $sql->leftJoin('cig_catalog', 'c', 'co.id_catalog = c.id_catalog');
        $sql->groupBy('co.id_catalog');
        $sql->orderBy('order_count DESC');
        $sql->limit($limit);

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Search orders
     */
    public static function searchOrders($search_term, $start = 0, $limit = 50)
    {
        $search_term = pSQL($search_term);
        
        $sql = new DbQuery();
        $sql->select('co.*, c.title as catalog_title');
        $sql->from('cig_catalog_order', 'co');
        $sql->leftJoin('cig_catalog', 'c', 'co.id_catalog = c.id_catalog');
        $sql->where('
            co.first_name LIKE "%' . $search_term . '%" OR
            co.last_name LIKE "%' . $search_term . '%" OR
            co.email LIKE "%' . $search_term . '%" OR
            co.company_name LIKE "%' . $search_term . '%" OR
            co.company_id LIKE "%' . $search_term . '%" OR
            c.title LIKE "%' . $search_term . '%"
        ');
        $sql->orderBy('co.date_add DESC');
        $sql->limit($limit, $start);

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Export orders to CSV
     */
    public static function exportToCSV($filters = [])
    {
        $sql = new DbQuery();
        $sql->select('
            co.id_order,
            c.title as catalog_title,
            co.company_name,
            co.company_id,
            co.first_name,
            co.last_name,
            co.email,
            co.phone,
            co.address,
            co.note,
            co.date_add
        ');
        $sql->from('cig_catalog_order', 'co');
        $sql->leftJoin('cig_catalog', 'c', 'co.id_catalog = c.id_catalog');
        
        // Apply filters
        if (isset($filters['id_catalog']) && $filters['id_catalog']) {
            $sql->where('co.id_catalog = ' . (int)$filters['id_catalog']);
        }
        
        if (isset($filters['date_from']) && $filters['date_from']) {
            $sql->where('co.date_add >= "' . pSQL($filters['date_from']) . '"');
        }
        
        if (isset($filters['date_to']) && $filters['date_to']) {
            $sql->where('co.date_add <= "' . pSQL($filters['date_to']) . '"');
        }
        
        $sql->orderBy('co.date_add DESC');

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Validate Czech company ID (IČO)
     */
    public static function validateCompanyId($company_id)
    {
        if (empty($company_id)) {
            return true; // Optional field
        }
        
        // Remove spaces and non-numeric characters
        $company_id = preg_replace('/[^0-9]/', '', $company_id);
        
        // Check length (8 digits for Czech IČO)
        if (strlen($company_id) !== 8) {
            return false;
        }
        
        // Validate checksum
        $sum = 0;
        for ($i = 0; $i < 7; $i++) {
            $sum += (int)$company_id[$i] * (8 - $i);
        }
        
        $remainder = $sum % 11;
        $checksum = ($remainder < 2) ? $remainder : 11 - $remainder;
        
        return (int)$company_id[7] === $checksum;
    }

    /**
     * Format company ID
     */
    public static function formatCompanyId($company_id)
    {
        if (empty($company_id)) {
            return '';
        }
        
        $company_id = preg_replace('/[^0-9]/', '', $company_id);
        
        if (strlen($company_id) === 8) {
            return substr($company_id, 0, 2) . ' ' . substr($company_id, 2, 3) . ' ' . substr($company_id, 5, 3);
        }
        
        return $company_id;
    }

    /**
     * Get full name
     */
    public function getFullName()
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    /**
     * Get formatted address
     */
    public function getFormattedAddress()
    {
        return nl2br(htmlspecialchars($this->address));
    }

    /**
     * Get catalog info
     */
    public function getCatalogInfo()
    {
        if (!$this->id_catalog) {
            return null;
        }
        
        $catalog = new Catalog($this->id_catalog);
        return $catalog->id ? $catalog : null;
    }
}
