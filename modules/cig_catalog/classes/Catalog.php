<?php
/**
 * Catalog Entity Class
 * 
 * <AUTHOR>
 * @copyright 2024 Cigogne
 * @license   Commercial
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class Catalog extends ObjectModel
{
    /** @var string Title of the catalog */
    public $title;
    
    /** @var string Description of the catalog */
    public $description;
    
    /** @var string Path to catalog image */
    public $image_path;
    
    /** @var string URL to catalog */
    public $catalog_url;
    
    /** @var string Path to catalog file */
    public $catalog_file;
    
    /** @var bool Is catalog marked as new */
    public $is_new;
    
    /** @var int Position for sorting */
    public $position;
    
    /** @var bool Is catalog active */
    public $active;
    
    /** @var string Date of creation */
    public $date_add;
    
    /** @var string Date of last update */
    public $date_upd;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'cig_catalog',
        'primary' => 'id_catalog',
        'multilang' => true,
        'fields' => [
            // Main fields
            'title' => [
                'type' => self::TYPE_STRING,
                'lang' => true,
                'validate' => 'isGenericName',
                'required' => true,
                'size' => 255
            ],
            'description' => [
                'type' => self::TYPE_HTML,
                'lang' => true,
                'validate' => 'isCleanHtml'
            ],
            'image_path' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isUrl',
                'size' => 500
            ],
            'catalog_url' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isUrl',
                'size' => 500
            ],
            'catalog_file' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isFileName',
                'size' => 500
            ],
            'is_new' => [
                'type' => self::TYPE_BOOL,
                'validate' => 'isBool'
            ],
            'position' => [
                'type' => self::TYPE_INT,
                'validate' => 'isUnsignedInt'
            ],
            'active' => [
                'type' => self::TYPE_BOOL,
                'validate' => 'isBool'
            ],
            'date_add' => [
                'type' => self::TYPE_DATE,
                'validate' => 'isDate'
            ],
            'date_upd' => [
                'type' => self::TYPE_DATE,
                'validate' => 'isDate'
            ]
        ]
    ];

    /**
     * Constructor
     */
    public function __construct($id = null, $id_lang = null, $id_shop = null)
    {
        parent::__construct($id, $id_lang, $id_shop);
        
        // Set default values
        if (!$this->id) {
            $this->active = true;
            $this->is_new = false;
            $this->position = $this->getHighestPosition() + 1;
        }
    }

    /**
     * Add catalog to database
     */
    public function add($auto_date = true, $null_values = false)
    {
        if ($auto_date) {
            $this->date_add = date('Y-m-d H:i:s');
            $this->date_upd = date('Y-m-d H:i:s');
        }
        
        if (!$this->position) {
            $this->position = $this->getHighestPosition() + 1;
        }
        
        return parent::add($auto_date, $null_values);
    }

    /**
     * Update catalog in database
     */
    public function update($null_values = false)
    {
        $this->date_upd = date('Y-m-d H:i:s');
        return parent::update($null_values);
    }

    /**
     * Delete catalog from database
     */
    public function delete()
    {
        // Delete associated files
        $this->deleteAssociatedFiles();
        
        // Reorganize positions
        $this->cleanPositions();
        
        return parent::delete();
    }

    /**
     * Get all active catalogs
     */
    public static function getActiveCatalogs($id_lang = null, $id_shop = null)
    {
        if (!$id_lang) {
            $id_lang = Context::getContext()->language->id;
        }
        
        if (!$id_shop) {
            $id_shop = Context::getContext()->shop->id;
        }

        $sql = new DbQuery();
        $sql->select('c.*, cl.title, cl.description');
        $sql->from('cig_catalog', 'c');
        $sql->leftJoin('cig_catalog_lang', 'cl', 'c.id_catalog = cl.id_catalog AND cl.id_lang = ' . (int)$id_lang);
        $sql->where('c.active = 1');
        $sql->orderBy('c.position ASC, c.id_catalog ASC');

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Get catalogs by position range
     */
    public static function getCatalogsByPosition($start = 0, $limit = 10, $id_lang = null)
    {
        if (!$id_lang) {
            $id_lang = Context::getContext()->language->id;
        }

        $sql = new DbQuery();
        $sql->select('c.*, cl.title, cl.description');
        $sql->from('cig_catalog', 'c');
        $sql->leftJoin('cig_catalog_lang', 'cl', 'c.id_catalog = cl.id_catalog AND cl.id_lang = ' . (int)$id_lang);
        $sql->where('c.active = 1');
        $sql->orderBy('c.position ASC');
        $sql->limit($limit, $start);

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Get new catalogs
     */
    public static function getNewCatalogs($id_lang = null, $limit = 5)
    {
        if (!$id_lang) {
            $id_lang = Context::getContext()->language->id;
        }

        $sql = new DbQuery();
        $sql->select('c.*, cl.title, cl.description');
        $sql->from('cig_catalog', 'c');
        $sql->leftJoin('cig_catalog_lang', 'cl', 'c.id_catalog = cl.id_catalog AND cl.id_lang = ' . (int)$id_lang);
        $sql->where('c.active = 1 AND c.is_new = 1');
        $sql->orderBy('c.date_add DESC');
        $sql->limit($limit);

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Get highest position
     */
    public function getHighestPosition()
    {
        $sql = new DbQuery();
        $sql->select('MAX(position)');
        $sql->from('cig_catalog');
        
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);
        return $result ? (int)$result : 0;
    }

    /**
     * Update position
     */
    public function updatePosition($way, $position)
    {
        if (!$res = Db::getInstance()->executeS('
            SELECT cp.`id_catalog`, cp.`position`
            FROM `' . _DB_PREFIX_ . 'cig_catalog` cp
            WHERE cp.`id_catalog` = ' . (int)$this->id . '
            ORDER BY cp.`position` ASC'
        )) {
            return false;
        }

        foreach ($res as $catalog) {
            if ((int)$catalog['id_catalog'] == (int)$this->id) {
                $moved_catalog = $catalog;
            }
        }

        if (!isset($moved_catalog) || !isset($position)) {
            return false;
        }

        return (Db::getInstance()->execute('
            UPDATE `' . _DB_PREFIX_ . 'cig_catalog`
            SET `position`= `position` ' . ($way ? '- 1' : '+ 1') . '
            WHERE `position`
            ' . ($way
                ? '> ' . (int)$moved_catalog['position'] . ' AND `position` <= ' . (int)$position
                : '< ' . (int)$moved_catalog['position'] . ' AND `position` >= ' . (int)$position)) &&
            Db::getInstance()->execute('
                UPDATE `' . _DB_PREFIX_ . 'cig_catalog`
                SET `position` = ' . (int)$position . '
                WHERE `id_catalog` = ' . (int)$moved_catalog['id_catalog']));
    }

    /**
     * Clean positions after deletion
     */
    public function cleanPositions()
    {
        $sql = 'SELECT `id_catalog` FROM `' . _DB_PREFIX_ . 'cig_catalog` ORDER BY `position` ASC';
        $result = Db::getInstance()->executeS($sql);
        
        $i = 1;
        foreach ($result as $row) {
            Db::getInstance()->execute('
                UPDATE `' . _DB_PREFIX_ . 'cig_catalog` 
                SET `position` = ' . (int)$i . ' 
                WHERE `id_catalog` = ' . (int)$row['id_catalog']
            );
            $i++;
        }
    }

    /**
     * Delete associated files
     */
    protected function deleteAssociatedFiles()
    {
        // Delete image file
        if ($this->image_path && file_exists(_PS_MODULE_DIR_ . 'cigcatalog/' . $this->image_path)) {
            @unlink(_PS_MODULE_DIR_ . 'cigcatalog/' . $this->image_path);
        }
        
        // Delete catalog file
        if ($this->catalog_file && file_exists(_PS_MODULE_DIR_ . 'cigcatalog/' . $this->catalog_file)) {
            @unlink(_PS_MODULE_DIR_ . 'cigcatalog/' . $this->catalog_file);
        }
    }

    /**
     * Get catalog image URL
     */
    public function getImageUrl()
    {
        if (!$this->image_path) {
            return null;
        }
        
        $context = Context::getContext();
        return $context->shop->getBaseURL(true) . 'modules/cigcatalog/' . $this->image_path;
    }

    /**
     * Get catalog file URL
     */
    public function getFileUrl()
    {
        if (!$this->catalog_file) {
            return null;
        }
        
        $context = Context::getContext();
        return $context->shop->getBaseURL(true) . 'modules/cigcatalog/' . $this->catalog_file;
    }

    /**
     * Check if catalog has file
     */
    public function hasFile()
    {
        return !empty($this->catalog_file) && file_exists(_PS_MODULE_DIR_ . 'cigcatalog/' . $this->catalog_file);
    }

    /**
     * Check if catalog has image
     */
    public function hasImage()
    {
        return !empty($this->image_path) && file_exists(_PS_MODULE_DIR_ . 'cigcatalog/' . $this->image_path);
    }
}
