<?php
/**
 * Catalog Configuration Entity Class
 * 
 * <AUTHOR>
 * @copyright 2024 Cigogne
 * @license   Commercial
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class CatalogConfig extends ObjectModel
{
    /** @var string Configuration name */
    public $name;
    
    /** @var string Configuration value */
    public $value;
    
    /** @var string Date of creation */
    public $date_add;
    
    /** @var string Date of last update */
    public $date_upd;

    /** @var array Cache for configuration values */
    protected static $cache = [];

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'cig_catalog_config',
        'primary' => 'id_config',
        'fields' => [
            'name' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isConfigName',
                'required' => true,
                'size' => 100
            ],
            'value' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isAnything'
            ],
            'date_add' => [
                'type' => self::TYPE_DATE,
                'validate' => 'isDate'
            ],
            'date_upd' => [
                'type' => self::TYPE_DATE,
                'validate' => 'isDate'
            ]
        ]
    ];

    /**
     * Add configuration to database
     */
    public function add($auto_date = true, $null_values = false)
    {
        if ($auto_date) {
            $this->date_add = date('Y-m-d H:i:s');
            $this->date_upd = date('Y-m-d H:i:s');
        }
        
        // Clear cache for this configuration
        if (isset(self::$cache[$this->name])) {
            unset(self::$cache[$this->name]);
        }
        
        return parent::add($auto_date, $null_values);
    }

    /**
     * Update configuration in database
     */
    public function update($null_values = false)
    {
        $this->date_upd = date('Y-m-d H:i:s');
        
        // Clear cache for this configuration
        if (isset(self::$cache[$this->name])) {
            unset(self::$cache[$this->name]);
        }
        
        return parent::update($null_values);
    }

    /**
     * Delete configuration from database
     */
    public function delete()
    {
        // Clear cache for this configuration
        if (isset(self::$cache[$this->name])) {
            unset(self::$cache[$this->name]);
        }
        
        return parent::delete();
    }

    /**
     * Get configuration value by name
     */
    public static function get($name, $default = null)
    {
        // Check cache first
        if (isset(self::$cache[$name])) {
            return self::$cache[$name];
        }
        
        $sql = new DbQuery();
        $sql->select('value');
        $sql->from('cig_catalog_config');
        $sql->where('name = "' . pSQL($name) . '"');
        
        $value = Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);
        
        if ($value === false) {
            $value = $default;
        }
        
        // Cache the value
        self::$cache[$name] = $value;
        
        return $value;
    }

    /**
     * Set configuration value
     */
    public static function set($name, $value)
    {
        // Check if configuration exists
        $sql = new DbQuery();
        $sql->select('id_config');
        $sql->from('cig_catalog_config');
        $sql->where('name = "' . pSQL($name) . '"');
        
        $id_config = Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);
        
        if ($id_config) {
            // Update existing configuration
            $config = new self($id_config);
            $config->value = $value;
            $result = $config->update();
        } else {
            // Create new configuration
            $config = new self();
            $config->name = $name;
            $config->value = $value;
            $result = $config->add();
        }
        
        // Update cache
        self::$cache[$name] = $value;
        
        return $result;
    }

    /**
     * Delete configuration by name
     */
    public static function deleteByName($name)
    {
        $sql = new DbQuery();
        $sql->select('id_config');
        $sql->from('cig_catalog_config');
        $sql->where('name = "' . pSQL($name) . '"');
        
        $id_config = Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);
        
        if ($id_config) {
            $config = new self($id_config);
            return $config->delete();
        }
        
        return true;
    }

    /**
     * Get all configurations
     */
    public static function getAll()
    {
        $sql = new DbQuery();
        $sql->select('*');
        $sql->from('cig_catalog_config');
        $sql->orderBy('name ASC');
        
        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Get configurations by prefix
     */
    public static function getByPrefix($prefix)
    {
        $sql = new DbQuery();
        $sql->select('*');
        $sql->from('cig_catalog_config');
        $sql->where('name LIKE "' . pSQL($prefix) . '%"');
        $sql->orderBy('name ASC');
        
        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Clear cache
     */
    public static function clearCache()
    {
        self::$cache = [];
    }

    /**
     * Get boolean configuration value
     */
    public static function getBool($name, $default = false)
    {
        $value = self::get($name, $default);
        return (bool)$value;
    }

    /**
     * Get integer configuration value
     */
    public static function getInt($name, $default = 0)
    {
        $value = self::get($name, $default);
        return (int)$value;
    }

    /**
     * Get float configuration value
     */
    public static function getFloat($name, $default = 0.0)
    {
        $value = self::get($name, $default);
        return (float)$value;
    }

    /**
     * Get array configuration value (JSON decoded)
     */
    public static function getArray($name, $default = [])
    {
        $value = self::get($name, null);
        
        if ($value === null) {
            return $default;
        }
        
        $decoded = json_decode($value, true);
        return $decoded !== null ? $decoded : $default;
    }

    /**
     * Set array configuration value (JSON encoded)
     */
    public static function setArray($name, $value)
    {
        return self::set($name, json_encode($value));
    }

    /**
     * Initialize default configurations
     */
    public static function initializeDefaults()
    {
        $defaults = [
            'CIGCATALOG_ITEMS_PER_PAGE' => '12',
            'CIGCATALOG_SHOW_NEW_BADGE' => '1',
            'CIGCATALOG_NEW_DAYS' => '30',
            'CIGCATALOG_ENABLE_ORDERS' => '1',
            'CIGCATALOG_ORDER_EMAIL_ADMIN' => '',
            'CIGCATALOG_ORDER_EMAIL_CUSTOMER' => '1',
            'CIGCATALOG_UPLOAD_MAX_SIZE' => '10485760', // 10MB
            'CIGCATALOG_ALLOWED_EXTENSIONS' => 'pdf,doc,docx,xls,xlsx,jpg,jpeg,png,gif',
            'CIGCATALOG_IMAGE_WIDTH' => '300',
            'CIGCATALOG_IMAGE_HEIGHT' => '300',
            'CIGCATALOG_THUMBNAIL_WIDTH' => '150',
            'CIGCATALOG_THUMBNAIL_HEIGHT' => '150',
            'CIGCATALOG_ENABLE_SEARCH' => '1',
            'CIGCATALOG_ENABLE_PAGINATION' => '1',
            'CIGCATALOG_ENABLE_SORTING' => '1',
            'CIGCATALOG_DEFAULT_SORT' => 'position',
            'CIGCATALOG_SHOW_DESCRIPTION' => '1',
            'CIGCATALOG_DESCRIPTION_LENGTH' => '200'
        ];
        
        foreach ($defaults as $name => $value) {
            // Only set if not already exists
            if (self::get($name, null) === null) {
                self::set($name, $value);
            }
        }
    }

    /**
     * Validate configuration name
     */
    public static function isConfigName($name)
    {
        return Validate::isGenericName($name) && preg_match('/^[A-Z_]+$/', $name);
    }

    /**
     * Get configuration groups for admin interface
     */
    public static function getConfigGroups()
    {
        return [
            'general' => [
                'title' => 'General Settings',
                'configs' => [
                    'CIGCATALOG_ITEMS_PER_PAGE',
                    'CIGCATALOG_SHOW_NEW_BADGE',
                    'CIGCATALOG_NEW_DAYS',
                    'CIGCATALOG_ENABLE_SEARCH',
                    'CIGCATALOG_ENABLE_PAGINATION',
                    'CIGCATALOG_ENABLE_SORTING',
                    'CIGCATALOG_DEFAULT_SORT'
                ]
            ],
            'display' => [
                'title' => 'Display Settings',
                'configs' => [
                    'CIGCATALOG_SHOW_DESCRIPTION',
                    'CIGCATALOG_DESCRIPTION_LENGTH',
                    'CIGCATALOG_IMAGE_WIDTH',
                    'CIGCATALOG_IMAGE_HEIGHT',
                    'CIGCATALOG_THUMBNAIL_WIDTH',
                    'CIGCATALOG_THUMBNAIL_HEIGHT'
                ]
            ],
            'orders' => [
                'title' => 'Order Settings',
                'configs' => [
                    'CIGCATALOG_ENABLE_ORDERS',
                    'CIGCATALOG_ORDER_EMAIL_ADMIN',
                    'CIGCATALOG_ORDER_EMAIL_CUSTOMER'
                ]
            ],
            'upload' => [
                'title' => 'Upload Settings',
                'configs' => [
                    'CIGCATALOG_UPLOAD_MAX_SIZE',
                    'CIGCATALOG_ALLOWED_EXTENSIONS'
                ]
            ]
        ];
    }
}
