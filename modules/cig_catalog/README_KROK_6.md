# Krok 6: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> admin funkce - DOKONČENO ✅

## P<PERSON><PERSON>led implementovaných funkcí

Tento krok implementuje pokročilé funkce pro administraci katalogů včetně drag&drop řazení, pokročilého upload managementu a konfigurace emailů.

## 🎯 Implementované funkce

### 1. Drag & Drop řazení katalogů ✅
- **Soubor**: `views/js/admin-catalog.js` (funkce `initializeSortable`)
- **CSS**: `views/css/admin-catalog.css` (drag&drop styly)
- **Endpoint**: `AdminCatalogController::reorderAction`
- **Route**: `admin_catalog_reorder`

**Funkce:**
- Drag & drop řazení v tabulce katalogů
- Vizuální feedback při přetahování
- AJAX aktualizace pozic
- Automatická aktualizace čísel pozic
- Fallback pro prohlížeče bez Sortable.js

### 2. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> upload management ✅
- **Soubor**: `views/js/file-upload.js` (třída `FileUploadManager`)
- **Komponenta**: `templates/admin/components/file-upload.html.twig`
- **CSS**: `views/css/admin-catalog.css` (upload styly)
- **Endpoint**: `AdminCatalogController::uploadAction`

**Funkce:**
- Drag & drop upload souborů
- Progress bary pro upload
- Validace typů a velikostí souborů
- Náhledy obrázků
- Concurrent uploads (max 3 současně)
- Error handling a retry mechanismus

### 3. Konfigurace emailů ✅
- **Formulář**: `src/Form/EmailConfigurationType.php`
- **Template**: `templates/admin/email/configuration.html.twig`
- **Endpoint**: `AdminCatalogController::emailConfigurationAction`
- **Test endpoint**: `AdminCatalogController::testEmailAction`

**Funkce:**
- Konfigurace SMTP serveru
- Nastavení email šablon
- Testování email konfigurace
- Podpora proměnných v šablonách
- Validace email adres

### 4. Bulk operace ✅
- **JavaScript**: `views/js/admin-catalog.js` (bulk actions)
- **Endpoint**: `AdminCatalogController::bulkAction`
- **CSS**: Loading stavy a animace

**Funkce:**
- Hromadná aktivace/deaktivace
- Hromadné smazání
- Výběr všech položek
- Loading stavy během operací
- Optimalizované UI aktualizace

### 5. AJAX endpointy a bezpečnost ✅
- **Security middleware**: Validace AJAX požadavků
- **Rate limiting**: Omezení frekvence požadavků
- **CSRF protection**: Ochrana proti CSRF útokům
- **Logging**: Detailní logování akcí
- **Error handling**: Standardizované error responses

## 🔧 Technické detaily

### Bezpečnostní opatření
```php
// Rate limiting
private function validateAjaxRequest(Request $request): bool
{
    $session = $request->getSession();
    $lastRequest = $session->get('last_ajax_request', 0);
    $now = time();
    
    if ($now - $lastRequest < 1) { // Max 1 request per second
        return false;
    }
    
    $session->set('last_ajax_request', $now);
    return true;
}
```

### File Upload validace
```php
private function sanitizeUploadedFile(UploadedFile $file): bool
{
    $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf', 'zip'];
    $allowedMimeTypes = ['image/jpeg', 'image/png', 'application/pdf'];
    
    return in_array($file->getClientOriginalExtension(), $allowedExtensions) &&
           in_array($file->getMimeType(), $allowedMimeTypes) &&
           $file->getSize() <= 50 * 1024 * 1024; // 50MB
}
```

### Drag & Drop implementace
```javascript
// Sortable initialization
new Sortable(tbody, {
    handle: '.drag-handle',
    animation: 150,
    ghostClass: 'sortable-ghost',
    chosenClass: 'sortable-chosen',
    dragClass: 'sortable-drag',
    onEnd: function(evt) {
        updatePositions();
    }
});
```

## 📁 Struktura souborů

```
modules/cig_catalog/
├── controllers/admin/
│   └── AdminCatalogController.php      # Rozšířený o AJAX endpointy
├── src/
│   └── Form/
│       └── EmailConfigurationType.php  # Formulář pro email config
├── templates/admin/
│   ├── components/
│   │   └── file-upload.html.twig      # Upload komponenta
│   └── email/
│       └── configuration.html.twig    # Email konfigurace
├── views/
│   ├── css/
│   │   └── admin-catalog.css          # Rozšířené styly
│   └── js/
│       ├── admin-catalog.js           # Rozšířený admin JS
│       └── file-upload.js             # Upload manager
└── config/
    └── routes.yml                     # Nové AJAX routes
```

## 🎨 CSS třídy

### Drag & Drop
- `.drag-handle` - Handle pro přetahování
- `.sortable-ghost` - Styl při přetahování
- `.sortable-chosen` - Vybraný element
- `.sortable-placeholder` - Placeholder při drop

### Upload
- `.file-upload-container` - Kontejner uploadu
- `.drop-zone` - Oblast pro drop
- `.upload-progress` - Progress kontejner
- `.upload-item` - Jednotlivý upload
- `.progress-fill` - Progress bar

### Bulk operace
- `.bulk-actions` - Kontejner bulk akcí
- `.bulk-action` - Tlačítko bulk akce
- `.loading` - Loading stav

## 🔗 AJAX Endpointy

| Endpoint | Metoda | Popis |
|----------|--------|-------|
| `/admin/catalog/reorder` | POST | Změna pořadí katalogů |
| `/admin/catalog/upload` | POST | Upload souborů |
| `/admin/catalog/bulk` | POST | Hromadné operace |
| `/admin/catalog/email-test` | POST | Test emailu |
| `/admin/catalog/{id}/delete-file/{type}` | POST | Smazání souboru |

## 📧 Email konfigurace

### Dostupné proměnné v šablonách
- `{{catalog_title}}` - Název katalogu
- `{{order_id}}` - Číslo objednávky
- `{{first_name}}` - Jméno zákazníka
- `{{last_name}}` - Příjmení zákazníka
- `{{company_name}}` - Název firmy
- `{{email}}` - Email zákazníka
- `{{phone}}` - Telefon zákazníka
- `{{address}}` - Adresa zákazníka
- `{{note}}` - Poznámka k objednávce
- `{{date_add}}` - Datum objednávky

### SMTP konfigurace
```php
// Příklad Gmail SMTP
'smtp_host' => 'smtp.gmail.com',
'smtp_port' => 587,
'smtp_encryption' => 'tls',
'smtp_username' => '<EMAIL>',
'smtp_password' => 'app_password'
```

## 🚀 Použití

### Inicializace upload manageru
```javascript
const uploadManager = new FileUploadManager('upload-container', {
    allowedTypes: {
        image: ['image/jpeg', 'image/png'],
        file: ['application/pdf']
    },
    multiple: false,
    uploadUrl: '/admin/catalog/upload',
    onSuccess: function(uploadItem, response) {
        console.log('Upload successful:', response);
    }
});
```

### Použití upload komponenty
```twig
{% include '@Modules/cig_catalog/templates/admin/components/file-upload.html.twig' with {
    id: 'catalog-image-upload',
    name: 'image',
    label: 'Obrázek katalogu',
    accept: 'image',
    current_file: catalog.image ?? null
} %}
```

## ✅ Testování

### Funkční testy
1. **Drag & Drop**: Přetáhněte katalogy v seznamu
2. **Upload**: Nahrajte obrázek nebo PDF soubor
3. **Bulk operace**: Vyberte více katalogů a proveďte hromadnou akci
4. **Email test**: Otestujte odesílání emailů

### Bezpečnostní testy
1. **CSRF**: Zkuste požadavek bez CSRF tokenu
2. **Rate limiting**: Pošlete více požadavků rychle za sebou
3. **File upload**: Zkuste nahrát nepodporovaný soubor

## 🔄 Následující kroky

Krok 6 je dokončen. Pokračujte krokem 7: Frontend stránka katalogů.

## 📝 Poznámky

- Všechny AJAX endpointy mají CSRF ochranu
- File upload má validaci typu a velikosti
- Drag & drop funguje i bez JavaScript (fallback)
- Email konfigurace podporuje SMTP i PHP mail()
- Bulk operace mají optimalizované UI aktualizace
